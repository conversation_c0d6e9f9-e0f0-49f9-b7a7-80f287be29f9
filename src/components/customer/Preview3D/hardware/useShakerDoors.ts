import {useCallback} from 'react';
import * as THREE from 'three';
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader';
import {useAppSelector} from 'store/customer';
import {shallowEqual} from 'react-redux';
import {MaterialType} from 'components/customer/Materials/entity';
import {getMaterial} from 'components/customer/Materials/store/selectors/materialSelector';
import {getDoor} from 'components/customer/Materials/store/selectors/doorSelector';
import {detectShakerDoor, ShakerDoorInfo} from '../helpers/shakerDoorHelper';

/**
 * Simple hook for loading shaker door GLB models
 */
const useShakerDoors = () => {
    // Get current material and door selections
    const selectedExteriorMaterial = useAppSelector(
        (state) => getMaterial(state, MaterialType.EXTERIOR),
        shallowEqual
    );
    const selectedDoor = useAppSelector(
        (state) => getDoor(state, MaterialType.EXTERIOR),
        shallowEqual
    );

    // Detect if current selection is a vinyl shaker door
    const shakerDoorInfo: ShakerDoorInfo = detectShakerDoor(
        selectedExteriorMaterial,
        selectedDoor
    );

    /**
     * Loads a shaker door GLB model
     */
    const loadShakerDoorModel = useCallback(
        (modelPath: string): Promise<THREE.Group> => {
            return new Promise((resolve, reject) => {
                const loader = new GLTFLoader();
                loader.load(
                    modelPath,
                    (gltf) => resolve(gltf.scene),
                    undefined,
                    (error) => {
                        console.error('❌ GLB loading failed:', error);
                        reject(error);
                    }
                );
            });
        },
        []
    );

    /**
     * Applies material texture to the shaker door model
     */
    const applyShakerDoorMaterial = useCallback(
        (model: THREE.Group, materialImage: string | null) => {
            if (!materialImage) return;

            const textureLoader = new THREE.TextureLoader();
            textureLoader.load(materialImage, (texture) => {
                const material = new THREE.MeshStandardMaterial({ map: texture });

                model.traverse((child) => {
                    if (child instanceof THREE.Mesh) {
                        child.material = material;
                    }
                });
            });
        },
        []
    );

    /**
     * Replaces flat door with shaker door GLB model
     */
    const applyShakerDoorToPanel = useCallback(
        async (originalMesh: THREE.Group): Promise<THREE.Group | null> => {
            if (!shakerDoorInfo.isVinylShakerDoor || !shakerDoorInfo.modelPath || !shakerDoorInfo.doorName) {
                return null;
            }

            try {
                // Load the shaker door model
                const shakerModel = await loadShakerDoorModel(shakerDoorInfo.modelPath);

                // Scale to match flat door size
                const scaleX = 1000;
                const scaleY = 1000;
                const scaleZ = 1000;

                shakerModel.scale.set(scaleX, scaleY, scaleZ);

                // Match position and rotation
                shakerModel.position.copy(originalMesh.position);
                shakerModel.rotation.copy(originalMesh.rotation);

                // Apply material
                if (selectedExteriorMaterial?.image) {
                    applyShakerDoorMaterial(shakerModel, selectedExteriorMaterial.image);
                }

                // Copy animation data
                shakerModel.userData = {...originalMesh.userData};
                shakerModel.name = originalMesh.name;

                return shakerModel;
            } catch (error) {
                console.error('❌ Error loading shaker door:', error);
                return null;
            }
        },
        [shakerDoorInfo, selectedExteriorMaterial?.image, loadShakerDoorModel, applyShakerDoorMaterial]
    );

    /**
     * Checks if shaker door should be used
     */
    const shouldUseShakerDoors = useCallback((): boolean => {
        return shakerDoorInfo.isVinylShakerDoor;
    }, [shakerDoorInfo.isVinylShakerDoor]);

    return {
        shouldUseShakerDoors,
        applyShakerDoorToPanel,
    };
};

export default useShakerDoors;
