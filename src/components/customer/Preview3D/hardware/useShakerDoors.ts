import {useRef, useCallback} from 'react';
import * as THREE from 'three';
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader';
import {useAppSelector} from 'store/customer';
import {shallowEqual} from 'react-redux';
import {MaterialType} from 'components/customer/Materials/entity';
import {getMaterial} from 'components/customer/Materials/store/selectors/materialSelector';
import {getDoor} from 'components/customer/Materials/store/selectors/doorSelector';
import {detectShakerDoor, ShakerDoorInfo} from '../helpers/shakerDoorHelper';

/**
 * Simple hook for loading shaker door GLB models
 */
const useShakerDoors = () => {
    const shakerDoorModels = useRef<Map<string, THREE.Group>>(new Map());

    // Get current material and door selections
    const selectedExteriorMaterial = useAppSelector(
        (state) => getMaterial(state, MaterialType.EXTERIOR),
        shallowEqual
    );
    const selectedDoor = useAppSelector(
        (state) => getDoor(state, MaterialType.EXTERIOR),
        shallowEqual
    );

    // Detect if current selection is a vinyl shaker door
    const shakerDoorInfo: ShakerDoorInfo = detectShakerDoor(
        selectedExteriorMaterial,
        selectedDoor
    );

    /**
     * Loads a shaker door GLB model and caches it
     */
    const loadShakerDoorModel = useCallback(
        (modelPath: string, doorName: string): Promise<THREE.Group> => {
            return new Promise((resolve, reject) => {
                // Check if model is already cached
                const cachedModel = shakerDoorModels.current.get(doorName);
                if (cachedModel) {
                    resolve(cachedModel.clone());
                    return;
                }

                const loader = new GLTFLoader();
                loader.load(
                    modelPath,
                    (gltf) => {
                        const model = gltf.scene;

                        // Cache the original model
                        shakerDoorModels.current.set(doorName, model.clone());

                        resolve(model);
                    },
                    undefined,
                    (error) => {
                        console.error('❌ GLB loading failed:', error);
                        reject(error);
                    }
                );
            });
        },
        []
    );

    /**
     * Applies material texture to the shaker door model
     */
    const applyShakerDoorMaterial = useCallback(
        (model: THREE.Group, materialImage: string | null) => {
            if (!materialImage) return;

            const textureLoader = new THREE.TextureLoader();
            textureLoader.load(materialImage, (texture) => {
                const material = new THREE.MeshStandardMaterial({ map: texture });

                model.traverse((child) => {
                    if (child instanceof THREE.Mesh) {
                        child.material = material;
                    }
                });
            });
        },
        []
    );

    /**
     * Replaces flat door with shaker door GLB model
     */
    const applyShakerDoorToPanel = useCallback(
        async (originalMesh: THREE.Group): Promise<THREE.Group | null> => {
            if (!shakerDoorInfo.isVinylShakerDoor || !shakerDoorInfo.modelPath || !shakerDoorInfo.doorName) {
                return null;
            }

            try {
                // Load the shaker door model
                const shakerModel = await loadShakerDoorModel(
                    shakerDoorInfo.modelPath,
                    shakerDoorInfo.doorName
                );

                // Match flat door dimensions
                const flatDoorBox = new THREE.Box3().setFromObject(originalMesh);
                const flatDoorSize = new THREE.Vector3();
                flatDoorBox.getSize(flatDoorSize);

                const shakerBox = new THREE.Box3().setFromObject(shakerModel);
                const shakerSize = new THREE.Vector3();
                shakerBox.getSize(shakerSize);

                // Scale to match flat door size
                const scaleX = flatDoorSize.x / shakerSize.x * 1000;
                const scaleY = flatDoorSize.y / shakerSize.y * 1000;
                const scaleZ = flatDoorSize.z / shakerSize.z * 1000;

                shakerModel.scale.set(scaleX, scaleY, scaleZ);

                // Match position and rotation
                shakerModel.position.copy(originalMesh.position);
                shakerModel.rotation.copy(originalMesh.rotation);

                // Apply material
                if (selectedExteriorMaterial?.image) {
                    applyShakerDoorMaterial(shakerModel, selectedExteriorMaterial.image);
                }

                // Copy animation data
                shakerModel.userData = {...originalMesh.userData};
                shakerModel.name = originalMesh.name;

                return shakerModel;
            } catch (error) {
                console.error('❌ Error loading shaker door:', error);
                return null;
            }
        },
        [shakerDoorInfo, selectedExteriorMaterial?.image, loadShakerDoorModel, applyShakerDoorMaterial]
    );

    /**
     * Checks if shaker door should be used
     */
    const shouldUseShakerDoors = useCallback((): boolean => {
        return shakerDoorInfo.isVinylShakerDoor;
    }, [shakerDoorInfo.isVinylShakerDoor]);

    return {
        shouldUseShakerDoors,
        applyShakerDoorToPanel,
    };
};

export default useShakerDoors;
